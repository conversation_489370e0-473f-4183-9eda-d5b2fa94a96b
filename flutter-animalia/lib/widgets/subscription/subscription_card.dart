import 'package:flutter/material.dart';
import '../../models/salon_subscription.dart';
import '../../models/subscription_tier.dart';

/// Widget displaying current subscription information
class SubscriptionCard extends StatelessWidget {
  final SalonSubscription subscription;
  final VoidCallback? onUpgrade;
  final VoidCallback? onCancel;

  const SubscriptionCard({
    super.key,
    required this.subscription,
    this.onUpgrade,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final tierColor = Color(int.parse(subscription.tier.colorHex.substring(1), radix: 16) + 0xFF000000);

    return Card(
      elevation: 4,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              tierColor.withOpacity(0.1),
              tierColor.withOpacity(0.05),
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with tier and status
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        subscription.tier.displayName,
                        style: theme.textTheme.headlineSmall?.copyWith(
                          color: tierColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      _buildStatusChip(),
                    ],
                  ),
                  CircleAvatar(
                    backgroundColor: tierColor,
                    radius: 24,
                    child: Text(
                      subscription.tier.displayName[0],
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Subscription details
              _buildDetailRow(
                icon: Icons.calendar_today,
                label: 'Start Date',
                value: _formatDate(subscription.startDate),
              ),

              if (subscription.endDate != null)
                _buildDetailRow(
                  icon: Icons.event,
                  label: 'End Date',
                  value: _formatDate(subscription.endDate!),
                ),

              if (subscription.nextBillingDate != null)
                _buildDetailRow(
                  icon: Icons.payment,
                  label: 'Next Billing',
                  value: _formatDate(subscription.nextBillingDate!),
                ),

              _buildDetailRow(
                icon: Icons.attach_money,
                label: 'Monthly Price',
                value: subscription.monthlyPrice > 0 
                    ? '${subscription.monthlyPrice.toStringAsFixed(2)} RON'
                    : 'Free',
              ),

              _buildDetailRow(
                icon: Icons.repeat,
                label: 'Billing Cycle',
                value: subscription.billingCycle.displayName,
              ),

              // Trial information
              if (subscription.isInTrial && subscription.trialDaysRemaining != null)
                Container(
                  margin: const EdgeInsets.only(top: 12),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue[200]!),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.schedule, color: Colors.blue[700], size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'Trial: ${subscription.trialDaysRemaining} days remaining',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: Colors.blue[700],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),

              const SizedBox(height: 20),

              // Action buttons
              Row(
                children: [
                  if (subscription.isCurrentlyActive && onUpgrade != null)
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: onUpgrade,
                        icon: const Icon(Icons.upgrade),
                        label: const Text('Upgrade'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: tierColor,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),

                  if (subscription.isCurrentlyActive && onUpgrade != null && onCancel != null)
                    const SizedBox(width: 12),

                  if (subscription.isCurrentlyActive && onCancel != null)
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: onCancel,
                        icon: const Icon(Icons.cancel),
                        label: const Text('Cancel'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.red,
                          side: const BorderSide(color: Colors.red),
                        ),
                      ),
                    ),

                  if (!subscription.isCurrentlyActive)
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: onUpgrade,
                        icon: const Icon(Icons.refresh),
                        label: const Text('Reactivate'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip() {
    Color chipColor;
    Color textColor = Colors.white;

    switch (subscription.status) {
      case SubscriptionStatus.active:
        chipColor = Colors.green;
        break;
      case SubscriptionStatus.trial:
        chipColor = Colors.blue;
        break;
      case SubscriptionStatus.cancelled:
        chipColor = Colors.red;
        break;
      case SubscriptionStatus.suspended:
        chipColor = Colors.orange;
        break;
      case SubscriptionStatus.expired:
        chipColor = Colors.grey;
        break;
    }

    return Chip(
      label: Text(
        subscription.status.displayName,
        style: TextStyle(
          color: textColor,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
      backgroundColor: chipColor,
      padding: const EdgeInsets.symmetric(horizontal: 8),
    );
  }

  Widget _buildDetailRow({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

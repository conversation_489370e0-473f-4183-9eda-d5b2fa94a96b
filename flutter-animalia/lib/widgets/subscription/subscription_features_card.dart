import 'package:flutter/material.dart';
import '../../models/salon_subscription.dart';

/// Widget displaying subscription features and limits
class SubscriptionFeaturesCard extends StatelessWidget {
  final SubscriptionFeatures features;

  const SubscriptionFeaturesCard({
    super.key,
    required this.features,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final tierColor = Color(int.parse(features.tier.colorHex.substring(1), radix: 16) + 0xFF000000);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.star,
                  color: tierColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Your Plan Features',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Description
            Text(
              features.description,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),

            const SizedBox(height: 16),

            // Features list
            ...features.tier.features.map((feature) => _buildFeatureItem(
              context,
              feature,
              true,
            )),

            const SizedBox(height: 16),

            // Limits section
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Plan Limits',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),

                  // SMS Quota
                  _buildLimitItem(
                    context,
                    icon: Icons.sms,
                    label: 'SMS Reminders',
                    value: '${features.smsQuota} per month',
                    color: Colors.blue,
                  ),

                  // Team Members
                  _buildLimitItem(
                    context,
                    icon: Icons.group,
                    label: 'Team Members',
                    value: features.maxTeamMembers != null 
                        ? '${features.maxTeamMembers} maximum'
                        : 'Unlimited',
                    color: Colors.green,
                  ),

                  // Salon Management
                  _buildLimitItem(
                    context,
                    icon: Icons.store,
                    label: 'Salon Management',
                    value: features.canChangeSalon ? 'Multiple salons' : 'Single salon',
                    color: Colors.purple,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Capabilities section
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: tierColor.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: tierColor.withOpacity(0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'What You Can Do',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: tierColor,
                    ),
                  ),
                  const SizedBox(height: 12),

                  _buildCapabilityItem(
                    context,
                    icon: Icons.sms,
                    label: 'Send SMS reminders to clients',
                    enabled: true,
                  ),

                  _buildCapabilityItem(
                    context,
                    icon: Icons.person_add,
                    label: 'Invite team members',
                    enabled: features.canInviteTeamMembers,
                  ),

                  _buildCapabilityItem(
                    context,
                    icon: Icons.swap_horiz,
                    label: 'Switch between salons',
                    enabled: features.canChangeSalon,
                  ),

                  _buildCapabilityItem(
                    context,
                    icon: Icons.calendar_today,
                    label: 'Manage appointments',
                    enabled: true,
                  ),

                  _buildCapabilityItem(
                    context,
                    icon: Icons.people,
                    label: 'Client database',
                    enabled: true,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(BuildContext context, String feature, bool included) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            included ? Icons.check_circle : Icons.cancel,
            color: included ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              feature,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: included ? null : Colors.grey,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLimitItem(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Icon(
              icon,
              color: color,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCapabilityItem(
    BuildContext context, {
    required IconData icon,
    required String label,
    required bool enabled,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            icon,
            color: enabled ? Colors.green : Colors.grey,
            size: 18,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: enabled ? null : Colors.grey,
              ),
            ),
          ),
          Icon(
            enabled ? Icons.check : Icons.close,
            color: enabled ? Colors.green : Colors.red,
            size: 16,
          ),
        ],
      ),
    );
  }
}

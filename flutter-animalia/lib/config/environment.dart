/// Environment configuration for the Animalia Grooming app
/// Handles different deployment environments and their specific configurations

import 'package:flutter/foundation.dart';

enum Environment {
  development,
  staging,
  production
}

class EnvironmentConfig {
  // Current environment - automatically detected based on build configuration
  static Environment? _currentEnvironment;

  /// Automatically detect environment based on build configuration
  static Environment _detectEnvironment() {
    // Check for compile-time environment variables first
    String? envFromBuild = String.fromEnvironment('FLUTTER_ENV');
    if (envFromBuild != null) {
      switch (envFromBuild.toLowerCase()) {
        case 'production':
        case 'prod':
          return Environment.production;
        case 'staging':
        case 'stage':
          return Environment.staging;
        case 'development':
        case 'dev':
        default:
          return Environment.production;
      }
    }

    // Fallback to debug mode detection
    if (kReleaseMode) {
      return Environment.production;
    } else if (kProfileMode) {
      return Environment.staging;
    } else {
      return Environment.development;
    }
  }

  /// Get the current environment
  static Environment get currentEnvironment {
    _currentEnvironment ??= _detectEnvironment();
    return _currentEnvironment!;
  }

  /// Force set environment (for testing purposes)
  static void setEnvironment(Environment env) {
    _currentEnvironment = env;
  }

  /// Get the API base URL for the current environment
  static String get apiBaseUrl {
    switch (currentEnvironment) {
      case Environment.development:
        return 'https://www.api.animalia-programari.ro'; // 'http://192.168.1.161:8080';
      case Environment.staging:
        return 'https://www.api.animalia-programari.ro'; // 'http://192.168.1.161:8080';
      case Environment.production:
        return 'https://www.api.animalia-programari.ro'; // 'http://192.168.1.161:8080';
    }
  }

  /// Get the Firebase configuration for the current environment
  static Map<String, String> get firebaseConfig {
    switch (currentEnvironment) {
      case Environment.development:
        return {
          'projectId': 'animalia-de0f1',
          'appId': '1:166674682070:ios:838930ed46df3a1213f977',
          'iosAppId': '1:166674682070:ios:838930ed46df3a1213f977',
          'androidAppId': '1:166674682070:android:838930ed46df3a1213f977',
          'webAppId': '1:166674682070:web:838930ed46df3a1213f977',
          'apiKey': 'AIzaSyBRxNAuAx7Y4QVHM2-plOHOqAUNtb9JXNg',
          'messagingSenderId': '166674682070',
          'authDomain': 'animalia-de0f1.firebaseapp.com',
          'googleClientId': '166674682070-c65gio5ehqqh24g8f2cssb30je1pgbto.apps.googleusercontent.com',
        };
      case Environment.staging:
        return {
          'projectId': 'animalia-de0f1',
          'appId': '1:166674682070:ios:838930ed46df3a1213f977',
          'iosAppId': '1:166674682070:ios:838930ed46df3a1213f977',
          'androidAppId': '1:166674682070:android:838930ed46df3a1213f977',
          'webAppId': '1:166674682070:web:838930ed46df3a1213f977',
          'apiKey': 'AIzaSyBRxNAuAx7Y4QVHM2-plOHOqAUNtb9JXNg',
          'messagingSenderId': '166674682070',
          'authDomain': 'animalia-de0f1.firebaseapp.com',
          'googleClientId': '166674682070-c65gio5ehqqh24g8f2cssb30je1pgbto.apps.googleusercontent.com',
        };
      case Environment.production:
        return {
          'projectId': 'animalia-de0f1',
          'appId': '1:166674682070:ios:838930ed46df3a1213f977',
          'iosAppId': '1:166674682070:ios:838930ed46df3a1213f977',
          'androidAppId': '1:166674682070:android:838930ed46df3a1213f977',
          'webAppId': '1:166674682070:web:838930ed46df3a1213f977',
          'apiKey': 'AIzaSyBRxNAuAx7Y4QVHM2-plOHOqAUNtb9JXNg',
          'messagingSenderId': '166674682070',
          'authDomain': 'animalia-de0f1.firebaseapp.com',
          'googleClientId': '166674682070-c65gio5ehqqh24g8f2cssb30je1pgbto.apps.googleusercontent.com',
        };
    }
  }

  /// Google Maps & Places API key used for location features
  static String get googleMapsApiKey {
    switch (currentEnvironment) {
      case Environment.development:
        return 'AIzaSyCO77ldStnRCjfZ3EThONj8F8X6d3EVWvI';
      case Environment.staging:
        return 'AIzaSyCO77ldStnRCjfZ3EThONj8F8X6d3EVWvI';
      case Environment.production:
        return 'AIzaSyCO77ldStnRCjfZ3EThONj8F8X6d3EVWvI';
    }
  }

  /// Get the app name for the current environment
  static String get appName {
    switch (currentEnvironment) {
      case Environment.development:
        return 'Animalia Grooming (Dev)';
      case Environment.staging:
        return 'Animalia Grooming (Staging)';
      case Environment.production:
        return 'Animalia Grooming';
    }
  }

  /// Get the bundle ID for the current environment
  static String get bundleId {
    switch (currentEnvironment) {
      case Environment.development:
        return 'ro.animaliaprogramari.animalia.dev';
      case Environment.staging:
        return 'ro.animaliaprogramari.animalia.staging';
      case Environment.production:
        return 'ro.animaliaprogramari.animalia';
    }
  }

  /// Check if the current environment is development
  static bool get isDevelopment => currentEnvironment == Environment.development;

  /// Check if the current environment is staging
  static bool get isStaging => currentEnvironment == Environment.staging;

  /// Check if the current environment is production
  static bool get isProduction => currentEnvironment == Environment.production;

  /// Get debug mode status
  static bool get isDebugMode => isDevelopment || isStaging;

  /// Get API timeout duration
  static Duration get apiTimeout {
    switch (currentEnvironment) {
      case Environment.development:
        return const Duration(seconds: 30);
      case Environment.staging:
        return const Duration(seconds: 20);
      case Environment.production:
        return const Duration(seconds: 15);
    }
  }

  /// Get logging level
  static String get logLevel {
    switch (currentEnvironment) {
      case Environment.development:
        return 'DEBUG';
      case Environment.staging:
        return 'INFO';
      case Environment.production:
        return 'ERROR';
    }
  }

  /// Print current environment configuration (debug mode only)
  static void printConfig() {
    if (isDebugMode) {
      debugPrint('🌍 Environment: ${currentEnvironment.name}');
      debugPrint('🔗 API Base URL: $apiBaseUrl');
      debugPrint('📱 App Name: $appName');
      debugPrint('📦 Bundle ID: $bundleId');
      debugPrint('⏱️ API Timeout: ${apiTimeout.inSeconds}s');
      debugPrint('📝 Log Level: $logLevel');
    }
  }
}

import 'package:flutter/foundation.dart';
import '../models/salon_subscription.dart';
import '../models/subscription_tier.dart';
import '../services/subscription_service.dart';

/// Provider for managing subscription state
class SubscriptionProvider extends ChangeNotifier {
  SalonSubscription? _currentSubscription;
  SubscriptionFeatures? _features;
  List<SalonSubscription> _history = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  SalonSubscription? get currentSubscription => _currentSubscription;
  SubscriptionFeatures? get features => _features;
  List<SalonSubscription> get history => _history;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Convenience getters
  bool get hasActiveSubscription => _currentSubscription?.isCurrentlyActive ?? false;
  SubscriptionTier get currentTier => _currentSubscription?.tier ?? SubscriptionTier.freelancer;
  int get smsQuota => _features?.smsQuota ?? 50;
  bool get canInviteTeamMembers => _features?.canInviteTeamMembers ?? false;
  bool get canChangeSalon => _features?.canChangeSalon ?? false;
  int? get maxTeamMembers => _features?.maxTeamMembers;
  bool get isInTrial => _currentSubscription?.isInTrial ?? false;
  int? get trialDaysRemaining => _currentSubscription?.trialDaysRemaining;

  /// Initialize subscription data
  Future<void> initialize() async {
    await loadCurrentSubscription();
    await loadSubscriptionFeatures();
  }

  /// Load current subscription
  Future<void> loadCurrentSubscription() async {
    _setLoading(true);
    _clearError();

    try {
      final response = await SubscriptionService.getCurrentSubscription();
      if (response.success) {
        _currentSubscription = response.data;
        notifyListeners();
      } else {
        _setError(response.error ?? 'Failed to load subscription');
      }
    } catch (e) {
      _setError('Failed to load subscription: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Load subscription features
  Future<void> loadSubscriptionFeatures() async {
    try {
      final response = await SubscriptionService.getSubscriptionFeatures();
      if (response.success) {
        _features = response.data;
        notifyListeners();
      } else {
        debugPrint('Failed to load subscription features: ${response.error}');
      }
    } catch (e) {
      debugPrint('Failed to load subscription features: $e');
    }
  }

  /// Load subscription history
  Future<void> loadSubscriptionHistory() async {
    _setLoading(true);
    _clearError();

    try {
      final response = await SubscriptionService.getSubscriptionHistory();
      if (response.success) {
        _history = response.data ?? [];
        notifyListeners();
      } else {
        _setError(response.error ?? 'Failed to load subscription history');
      }
    } catch (e) {
      _setError('Failed to load subscription history: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Upgrade subscription
  Future<bool> upgradeSubscription({
    required SubscriptionTier newTier,
    required double newPrice,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await SubscriptionService.upgradeSubscription(
        newTier: newTier,
        newPrice: newPrice,
      );

      if (response.success) {
        _currentSubscription = response.data;
        await loadSubscriptionFeatures(); // Refresh features
        notifyListeners();
        return true;
      } else {
        _setError(response.error ?? 'Failed to upgrade subscription');
        return false;
      }
    } catch (e) {
      _setError('Failed to upgrade subscription: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Cancel subscription
  Future<bool> cancelSubscription({String? reason}) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await SubscriptionService.cancelSubscription(reason: reason);

      if (response.success) {
        _currentSubscription = response.data;
        await loadSubscriptionFeatures(); // Refresh features
        notifyListeners();
        return true;
      } else {
        _setError(response.error ?? 'Failed to cancel subscription');
        return false;
      }
    } catch (e) {
      _setError('Failed to cancel subscription: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Reactivate subscription
  Future<bool> reactivateSubscription() async {
    _setLoading(true);
    _clearError();

    try {
      final response = await SubscriptionService.reactivateSubscription();

      if (response.success) {
        _currentSubscription = response.data;
        await loadSubscriptionFeatures(); // Refresh features
        notifyListeners();
        return true;
      } else {
        _setError(response.error ?? 'Failed to reactivate subscription');
        return false;
      }
    } catch (e) {
      _setError('Failed to reactivate subscription: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Check if SMS quota allows sending messages
  Future<bool> checkSmsQuota(int currentUsage) async {
    try {
      final response = await SubscriptionService.checkSmsQuota(currentUsage);
      return response.success && (response.data ?? false);
    } catch (e) {
      debugPrint('Failed to check SMS quota: $e');
      return false;
    }
  }

  /// Check if subscription allows team member invitations
  Future<bool> checkTeamInvitation(int currentTeamSize) async {
    try {
      final response = await SubscriptionService.checkTeamInvitation(currentTeamSize);
      return response.success && (response.data ?? false);
    } catch (e) {
      debugPrint('Failed to check team invitation: $e');
      return false;
    }
  }

  /// Check if subscription allows salon changes
  Future<bool> checkSalonChange() async {
    try {
      final response = await SubscriptionService.checkSalonChange();
      return response.success && (response.data ?? false);
    } catch (e) {
      debugPrint('Failed to check salon change: $e');
      return false;
    }
  }

  /// Refresh all subscription data
  Future<void> refresh() async {
    await loadCurrentSubscription();
    await loadSubscriptionFeatures();
  }

  /// Clear subscription data (e.g., when logging out)
  void clear() {
    _currentSubscription = null;
    _features = null;
    _history = [];
    _isLoading = false;
    _error = null;
    notifyListeners();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }
}

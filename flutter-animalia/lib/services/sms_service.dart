import '../models/appointment.dart';
import '../models/client.dart';
import '../models/api_response.dart';
import 'api_service.dart';
import 'sms_quota_service.dart';

/// Abstraction over [ApiService] to facilitate testing.
abstract class ApiServiceInterface {
  Future<ApiResponse<T>> post<T>(String endpoint,
      {Map<String, dynamic>? body, T Function(dynamic)? fromJson});

  Future<ApiResponse<T>> get<T>(String endpoint,
      {Map<String, String>? queryParams, T Function(dynamic)? fromJson});

  Future<ApiResponse<T>> put<T>(String endpoint,
      {Map<String, dynamic>? body, T Function(dynamic)? fromJson});

  Future<ApiResponse<T>> delete<T>(String endpoint);
}

class _RealApiService implements ApiServiceInterface {
  @override
  Future<ApiResponse<T>> post<T>(String endpoint,
          {Map<String, dynamic>? body, T Function(dynamic)? fromJson}) =>
      ApiService.post<T>(endpoint, body: body, fromJson: fromJson);

  @override
  Future<ApiResponse<T>> get<T>(String endpoint,
          {Map<String, String>? queryParams, T Function(dynamic)? fromJson}) =>
      ApiService.get<T>(endpoint,
          queryParams: queryParams, fromJson: fromJson);

  @override
  Future<ApiResponse<T>> put<T>(String endpoint,
          {Map<String, dynamic>? body, T Function(dynamic)? fromJson}) =>
      ApiService.put<T>(endpoint, body: body, fromJson: fromJson);

  @override
  Future<ApiResponse<T>> delete<T>(String endpoint) =>
      ApiService.delete<T>(endpoint);
}

/// [SmsService] uses [ApiServiceInterface] so it can be mocked in tests.
class SmsService {
  /// API dependency. Tests can replace this with a mock.
  static ApiServiceInterface api = _RealApiService();
  // Send confirmation SMS
  static Future<ApiResponse<Map<String, dynamic>>> sendConfirmationSms(
    Appointment appointment,
    Client client,
  ) async {
    if (!await SmsQuotaService.canSendSms()) {
      return ApiResponse.error('Nu mai aveti SMS-uri disponibile pentru planul curent');
    }
    final body = <String, dynamic>{
      'phoneNumber': client.phone,
      'type': 'confirmation',
      'appointmentId': appointment.id,
      'clientId': client.id,
      'appointmentData': appointment.toJson(),
      'clientData': client.toJson(),
    };

    final response = await api.post<Map<String, dynamic>>(
      '/api/sms/send',
      body: body,
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    if (response.success) {
      await SmsQuotaService.decrementSmsCount();
    }

    return response;
  }

  // Send reminder SMS
  static Future<ApiResponse<Map<String, dynamic>>> sendReminderSms(
    Appointment appointment,
    Client client,
  ) async {
    if (!await SmsQuotaService.canSendSms()) {
      return ApiResponse.error('Nu mai aveti SMS-uri disponibile pentru planul curent');
    }
    final body = <String, dynamic>{
      'phoneNumber': client.phone,
      'type': 'reminder',
      'appointmentId': appointment.id,
      'clientId': client.id,
      'appointmentData': appointment.toJson(),
      'clientData': client.toJson(),
    };

    final response = await api.post<Map<String, dynamic>>(
      '/api/sms/send',
      body: body,
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    if (response.success) {
      await SmsQuotaService.decrementSmsCount();
    }

    return response;
  }

  // Send follow-up SMS
  static Future<ApiResponse<Map<String, dynamic>>> sendFollowUpSms(
    Appointment appointment,
    Client client,
  ) async {
    if (await SmsQuotaService.getRemainingSms() <= 0) {
      return ApiResponse.error('Nu mai aveti SMS-uri disponibile');
    }
    final body = <String, dynamic>{
      'phoneNumber': client.phone,
      'type': 'followup',
      'appointmentId': appointment.id,
      'clientId': client.id,
      'appointmentData': appointment.toJson(),
      'clientData': client.toJson(),
    };

    final response = await api.post<Map<String, dynamic>>(
      '/api/sms/send',
      body: body,
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    if (response.success) {
      await SmsQuotaService.decrementSmsCount();
    }

    return response;
  }

  // Send cancellation SMS
  static Future<ApiResponse<Map<String, dynamic>>> sendCancellationSms(
    Appointment appointment,
    Client client,
  ) async {
    if (await SmsQuotaService.getRemainingSms() <= 0) {
      return ApiResponse.error('Nu mai aveti SMS-uri disponibile');
    }
    final body = <String, dynamic>{
      'phoneNumber': client.phone,
      'type': 'cancellation',
      'appointmentId': appointment.id,
      'clientId': client.id,
      'appointmentData': appointment.toJson(),
      'clientData': client.toJson(),
    };

    final response = await api.post<Map<String, dynamic>>(
      '/api/sms/send',
      body: body,
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    if (response.success) {
      await SmsQuotaService.decrementSmsCount();
    }

    return response;
  }

  // Send custom SMS (removed duplicate - using the one at the end of file)

  // Send bulk SMS
  static Future<ApiResponse<List<Map<String, dynamic>>>> sendBulkSms({
    required List<Appointment> appointments,
    required List<Client> clients,
    required String messageType,
  }) async {
    if (await SmsQuotaService.getRemainingSms() <= 0) {
      return ApiResponse.error('Nu mai aveti SMS-uri disponibile');
    }
    final body = <String, dynamic>{
      'appointments': appointments.map((a) => a.toJson()).toList(),
      'clients': clients.map((c) => c.toJson()).toList(),
      'messageType': messageType,
    };

    final response = await api.post<List<Map<String, dynamic>>>(
      '/api/sms/bulk',
      body: body,
      fromJson: (data) => (data as List).map((item) => Map<String, dynamic>.from(item)).toList(),
    );

    if (response.success) {
      await SmsQuotaService.decrementSmsCount();
    }

    return response;
  }

  // Get SMS history
  static Future<ApiResponse<List<Map<String, dynamic>>>> getSmsHistory({
    int? limit,
    String? type,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final queryParams = <String, String>{};

    if (limit != null) queryParams['limit'] = limit.toString();
    if (type != null) queryParams['type'] = type;
    if (startDate != null) {
      queryParams['startDate'] = startDate.toIso8601String().split('T')[0];
    }
    if (endDate != null) {
      queryParams['endDate'] = endDate.toIso8601String().split('T')[0];
    }

    final response = await api.get<List<Map<String, dynamic>>>(
      '/api/sms/history',
      queryParams: queryParams.isNotEmpty ? queryParams : null,
      fromJson: (data) => (data as List).map((item) => Map<String, dynamic>.from(item)).toList(),
    );

    return response;
  }

  // Get SMS statistics
  static Future<ApiResponse<Map<String, dynamic>>> getSmsStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final queryParams = <String, String>{};

    if (startDate != null) {
      queryParams['startDate'] = startDate.toIso8601String().split('T')[0];
    }
    if (endDate != null) {
      queryParams['endDate'] = endDate.toIso8601String().split('T')[0];
    }

    final response = await api.get<Map<String, dynamic>>(
      '/api/sms/statistics',
      queryParams: queryParams.isNotEmpty ? queryParams : null,
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Get available SMS providers
  static Future<ApiResponse<List<Map<String, dynamic>>>> getAvailableProviders() async {
    final response = await api.get<List<Map<String, dynamic>>>(
      '/api/sms/providers',
      fromJson: (data) => (data as List).map((item) => Map<String, dynamic>.from(item)).toList(),
    );

    return response;
  }

  // Update SMS provider settings
  static Future<ApiResponse<Map<String, dynamic>>> updateProviderSettings({
    required String provider,
    required Map<String, dynamic> settings,
  }) async {
    final body = <String, dynamic>{
      'provider': provider,
      'settings': settings,
    };

    final response = await api.put<Map<String, dynamic>>(
      '/api/sms/provider-settings',
      body: body,
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }


  // Preview SMS message
  static Future<ApiResponse<String>> previewSmsMessage({
    required String templateType,
    required Appointment appointment,
    required Client client,
  }) async {
    final body = <String, dynamic>{
      'templateType': templateType,
      'appointmentData': appointment.toJson(),
      'clientData': client.toJson(),
    };

    final response = await api.post<String>(
      '/api/sms/preview',
      body: body,
      fromJson: (data) => data.toString(),
    );

    return response;
  }

  // Test SMS configuration
  static Future<ApiResponse<Map<String, dynamic>>> testSmsConfiguration({
    required String provider,
    required String testPhoneNumber,
  }) async {
    final body = <String, dynamic>{
      'provider': provider,
      'testPhoneNumber': testPhoneNumber,
    };

    final response = await api.post<Map<String, dynamic>>(
      '/api/sms/test',
      body: body,
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }



  // Validate SMS message
  static String? validateSmsMessage(String message) {
    if (message.trim().isEmpty) {
      return 'Mesajul SMS nu poate fi gol';
    }

    if (message.length > 160) {
      return 'Mesajul SMS nu poate avea mai mult de 160 de caractere';
    }

    return null; // Valid message
  }



  // Calculate SMS cost
  static double calculateSmsCost(int messageCount, String provider) {
    const providerCosts = {
      'orange': 0.05,
      'vodafone': 0.06,
      'telekom': 0.07,
      'smsgateway': 0.04,
    };

    final costPerSms = providerCosts[provider] ?? 0.05;
    return messageCount * costPerSms;
  }

  // Export SMS history
  static Future<ApiResponse<String>> exportSmsHistory({
    DateTime? startDate,
    DateTime? endDate,
    String format = 'csv',
    List<String>? types,
  }) async {
    final body = <String, dynamic>{
      'format': format,
      if (startDate != null) 'startDate': startDate.toIso8601String().split('T')[0],
      if (endDate != null) 'endDate': endDate.toIso8601String().split('T')[0],
      if (types != null) 'types': types,
    };

    final response = await api.post<String>(
      '/api/sms/export',
      body: body,
      fromJson: (data) => data.toString(),
    );

    return response;
  }

  // Send custom SMS message
  static Future<ApiResponse<Map<String, dynamic>>> sendCustomSms({
    required String phoneNumber,
    required String message,
    required String type,
    String? appointmentId,
  }) async {
    if (await SmsQuotaService.getRemainingSms() <= 0) {
      return ApiResponse.error('Nu mai aveti SMS-uri disponibile');
    }
    final body = <String, dynamic>{
      'phoneNumber': phoneNumber,
      'message': message,
      'type': type,
      if (appointmentId != null) 'appointmentId': appointmentId,
    };

    final response = await api.post<Map<String, dynamic>>(
      '/api/sms/send-custom',
      body: body,
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    if (response.success) {
      await SmsQuotaService.decrementSmsCount();
    }

    return response;
  }
}

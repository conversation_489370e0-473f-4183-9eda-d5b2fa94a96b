import 'package:flutter/foundation.dart';
import '../models/api_response.dart';
import '../models/salon_subscription.dart';
import '../models/subscription_tier.dart';
import 'api_service.dart';
import 'auth/auth_service.dart';

/// Service for managing salon subscriptions
class SubscriptionService {
  
  /// Get current subscription for the active salon
  static Future<ApiResponse<SalonSubscription?>> getCurrentSubscription() async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse.error('No active salon found');
      }

      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/v1/salons/$salonId/subscription',
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final subscription = SalonSubscription.fromJson(response.data!);
        return ApiResponse.success(subscription);
      } else if (response.statusCode == 404) {
        // No subscription found
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error(response.error ?? 'Failed to get subscription');
      }
    } catch (e) {
      debugPrint('❌ Error getting current subscription: $e');
      return ApiResponse.error('Failed to get subscription: $e');
    }
  }

  /// Get subscription features for the active salon
  static Future<ApiResponse<SubscriptionFeatures>> getSubscriptionFeatures() async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse.error('No active salon found');
      }

      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/v1/salons/$salonId/subscription/features',
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final features = SubscriptionFeatures.fromJson(response.data!);
        return ApiResponse.success(features);
      } else {
        return ApiResponse.error(response.error ?? 'Failed to get subscription features');
      }
    } catch (e) {
      debugPrint('❌ Error getting subscription features: $e');
      return ApiResponse.error('Failed to get subscription features: $e');
    }
  }

  /// Get subscription history for the active salon
  static Future<ApiResponse<List<SalonSubscription>>> getSubscriptionHistory() async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse.error('No active salon found');
      }

      final response = await ApiService.get<List<dynamic>>(
        '/api/v1/salons/$salonId/subscription/history',
        fromJson: (data) => List<dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final subscriptions = response.data!
            .map((json) => SalonSubscription.fromJson(Map<String, dynamic>.from(json)))
            .toList();
        return ApiResponse.success(subscriptions);
      } else {
        return ApiResponse.error(response.error ?? 'Failed to get subscription history');
      }
    } catch (e) {
      debugPrint('❌ Error getting subscription history: $e');
      return ApiResponse.error('Failed to get subscription history: $e');
    }
  }

  /// Upgrade subscription to a higher tier
  static Future<ApiResponse<SalonSubscription>> upgradeSubscription({
    required SubscriptionTier newTier,
    required double newPrice,
  }) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse.error('No active salon found');
      }

      final body = {
        'newTier': newTier.value,
        'newPrice': newPrice,
      };

      final response = await ApiService.put<Map<String, dynamic>>(
        '/api/v1/salons/$salonId/subscription/upgrade',
        body: body,
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final subscription = SalonSubscription.fromJson(response.data!);
        return ApiResponse.success(subscription);
      } else {
        return ApiResponse.error(response.error ?? 'Failed to upgrade subscription');
      }
    } catch (e) {
      debugPrint('❌ Error upgrading subscription: $e');
      return ApiResponse.error('Failed to upgrade subscription: $e');
    }
  }

  /// Cancel subscription
  static Future<ApiResponse<SalonSubscription>> cancelSubscription({
    String? reason,
  }) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse.error('No active salon found');
      }

      final body = {
        if (reason != null) 'reason': reason,
      };

      final response = await ApiService.put<Map<String, dynamic>>(
        '/api/v1/salons/$salonId/subscription/cancel',
        body: body,
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final subscription = SalonSubscription.fromJson(response.data!);
        return ApiResponse.success(subscription);
      } else {
        return ApiResponse.error(response.error ?? 'Failed to cancel subscription');
      }
    } catch (e) {
      debugPrint('❌ Error cancelling subscription: $e');
      return ApiResponse.error('Failed to cancel subscription: $e');
    }
  }

  /// Reactivate cancelled subscription
  static Future<ApiResponse<SalonSubscription>> reactivateSubscription() async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse.error('No active salon found');
      }

      final response = await ApiService.put<Map<String, dynamic>>(
        '/api/v1/salons/$salonId/subscription/reactivate',
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final subscription = SalonSubscription.fromJson(response.data!);
        return ApiResponse.success(subscription);
      } else {
        return ApiResponse.error(response.error ?? 'Failed to reactivate subscription');
      }
    } catch (e) {
      debugPrint('❌ Error reactivating subscription: $e');
      return ApiResponse.error('Failed to reactivate subscription: $e');
    }
  }

  /// Check if SMS quota allows sending messages
  static Future<ApiResponse<bool>> checkSmsQuota(int currentUsage) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse.error('No active salon found');
      }

      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/v1/salons/$salonId/subscription/validation/sms-quota?currentUsage=$currentUsage',
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final isValid = response.data!['isValid'] as bool;
        return ApiResponse.success(isValid);
      } else {
        return ApiResponse.error(response.error ?? 'Failed to check SMS quota');
      }
    } catch (e) {
      debugPrint('❌ Error checking SMS quota: $e');
      return ApiResponse.error('Failed to check SMS quota: $e');
    }
  }

  /// Check if subscription allows team member invitations
  static Future<ApiResponse<bool>> checkTeamInvitation(int currentTeamSize) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse.error('No active salon found');
      }

      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/v1/salons/$salonId/subscription/validation/team-invitation?currentTeamSize=$currentTeamSize',
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final isValid = response.data!['isValid'] as bool;
        return ApiResponse.success(isValid);
      } else {
        return ApiResponse.error(response.error ?? 'Failed to check team invitation');
      }
    } catch (e) {
      debugPrint('❌ Error checking team invitation: $e');
      return ApiResponse.error('Failed to check team invitation: $e');
    }
  }

  /// Check if subscription allows salon changes
  static Future<ApiResponse<bool>> checkSalonChange() async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse.error('No active salon found');
      }

      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/v1/salons/$salonId/subscription/validation/salon-change',
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final isValid = response.data!['isValid'] as bool;
        return ApiResponse.success(isValid);
      } else {
        return ApiResponse.error(response.error ?? 'Failed to check salon change');
      }
    } catch (e) {
      debugPrint('❌ Error checking salon change: $e');
      return ApiResponse.error('Failed to check salon change: $e');
    }
  }
}

import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'api_service.dart';
import 'auth/auth_service.dart';
import 'subscription_service.dart';

/// Service for managing user's SMS quota
class SmsQuotaService {
  static const String _remainingSmsKey = 'remaining_sms';
  static const int _defaultSmsLimit = 30;

  /// ValueNotifier to notify listeners when SMS count changes
  static final ValueNotifier<int> _smsCountNotifier = ValueNotifier<int>(_defaultSmsLimit);

  /// Get the ValueNotifier for SMS count changes
  static ValueNotifier<int> get smsCountNotifier => _smsCountNotifier;

  /// Get remaining SMS count (prioritizes backend data)
  static Future<int> getRemainingSms({bool forceBackendRefresh = false}) async {
    if (forceBackendRefresh) {
      try {
        await refreshFromBackend();
      } catch (e) {
        debugPrint('⚠️ Failed to refresh from backend during getRemainingSms: $e');
      }
    }

    final prefs = await SharedPreferences.getInstance();
    final count = prefs.getInt(_remainingSmsKey) ?? await _getDefaultSmsLimit();
    // Update notifier with current value
    _smsCountNotifier.value = count;
    return count;
  }

  /// Get default SMS limit based on subscription tier
  static Future<int> _getDefaultSmsLimit() async {
    try {
      final featuresResponse = await SubscriptionService.getSubscriptionFeatures();
      if (featuresResponse.success && featuresResponse.data != null) {
        return featuresResponse.data!.smsQuota;
      }
    } catch (e) {
      debugPrint('⚠️ Failed to get subscription SMS quota: $e');
    }
    return _defaultSmsLimit; // Fallback to default
  }

  /// Decrease SMS count by one when a message is sent
  static Future<void> decrementSmsCount() async {
    final prefs = await SharedPreferences.getInstance();
    final current = prefs.getInt(_remainingSmsKey) ?? _defaultSmsLimit;
    final updated = current > 0 ? current - 1 : 0;
    await prefs.setInt(_remainingSmsKey, updated);
    // Notify listeners of the change
    _smsCountNotifier.value = updated;
  }

  /// Reset SMS count to default or custom value
  static Future<void> resetSmsCount([int? count]) async {
    final prefs = await SharedPreferences.getInstance();
    final newCount = count ?? await _getDefaultSmsLimit();
    await prefs.setInt(_remainingSmsKey, newCount);
    // Notify listeners of the change
    _smsCountNotifier.value = newCount;
  }

  /// Check if SMS can be sent based on subscription limits
  static Future<bool> canSendSms() async {
    try {
      final currentUsage = await getCurrentSmsUsage();
      final checkResponse = await SubscriptionService.checkSmsQuota(currentUsage);
      return checkResponse.success && (checkResponse.data ?? false);
    } catch (e) {
      debugPrint('⚠️ Failed to check SMS quota: $e');
      // Fallback to local check
      final remaining = await getRemainingSms();
      return remaining > 0;
    }
  }

  /// Get current SMS usage for the month
  static Future<int> getCurrentSmsUsage() async {
    try {
      final quotaStatus = await getQuotaStatusFromBackend();
      if (quotaStatus != null) {
        return quotaStatus['usedQuota'] ?? 0;
      }
    } catch (e) {
      debugPrint('⚠️ Failed to get SMS usage: $e');
    }

    // Fallback: calculate from remaining and total
    final remaining = await getRemainingSms();
    final total = await _getDefaultSmsLimit();
    return total - remaining;
  }

  /// Sync SMS count with backend after appointment creation
  /// This handles the case where backend sends SMS automatically
  static Future<void> syncAfterAppointmentCreation() async {
    try {
      // Try to refresh from backend to get the actual count after SMS was sent
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        debugPrint('⚠️ No salon ID available for SMS quota refresh, using local decrement');
        await decrementSmsCount();
        return;
      }

      final response = await ApiService.get<Map<String, dynamic>>('/api/sms/quota/$salonId/remaining');
      if (response.success && response.data != null) {
        final backendCount = response.data!['remainingQuota'] as int;
        await _updateLocalCount(backendCount);
        debugPrint('📱 SMS count synced from backend: $backendCount');
      } else {
        debugPrint('⚠️ Failed to get SMS count from backend: ${response.error}, using local decrement');
        await decrementSmsCount();
      }
      debugPrint('📱 SMS count synced after appointment creation');
    } catch (e) {
      debugPrint('⚠️ Failed to sync SMS count: $e, using local decrement');
      // Fallback to local decrement
      await decrementSmsCount();
    }
  }

  /// Refresh SMS count from backend
  static Future<bool> refreshFromBackend() async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        debugPrint('⚠️ No salon ID available for SMS quota refresh');
        return false;
      }

      final response = await ApiService.get<Map<String, dynamic>>('/api/sms/quota/$salonId/remaining');
      if (response.success && response.data != null) {
        final backendCount = response.data!['remainingQuota'] as int;
        await _updateLocalCount(backendCount);
        debugPrint('📱 SMS count refreshed from backend: $backendCount');
        return true;
      } else {
        debugPrint('⚠️ Failed to get SMS count from backend: ${response.error}');
        return false;
      }
    } catch (e) {
      debugPrint('⚠️ Failed to refresh SMS count from backend: $e');
      return false;
    }
  }

  /// Get SMS quota status from backend (full status)
  static Future<Map<String, dynamic>?> getQuotaStatusFromBackend() async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        debugPrint('⚠️ No salon ID available for SMS quota status');
        return null;
      }

      final response = await ApiService.get<Map<String, dynamic>>('/api/sms/quota/$salonId');
      if (response.success && response.data != null) {
        final quotaStatus = response.data!;
        // Update local count with backend data
        final remainingQuota = quotaStatus['remainingQuota'] as int;
        await _updateLocalCount(remainingQuota);
        debugPrint('📱 SMS quota status refreshed from backend: $remainingQuota remaining');
        return quotaStatus;
      } else {
        debugPrint('⚠️ Failed to get SMS quota status from backend: ${response.error}');
        return null;
      }
    } catch (e) {
      debugPrint('⚠️ Failed to get SMS quota status from backend: $e');
      return null;
    }
  }

  /// Update local count and notify listeners
  static Future<void> _updateLocalCount(int count) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_remainingSmsKey, count);
    _smsCountNotifier.value = count;
  }
}

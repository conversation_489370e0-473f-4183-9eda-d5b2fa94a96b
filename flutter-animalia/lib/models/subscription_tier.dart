/// Enum representing the different subscription tiers available for salons
enum SubscriptionTier {
  freelancer('FREELANCER'),
  team('TEAM'),
  enterprise('ENTERPRISE');

  const SubscriptionTier(this.value);
  final String value;

  /// Create SubscriptionTier from string value
  static SubscriptionTier fromString(String value) {
    switch (value.toUpperCase()) {
      case 'FREELANCER':
        return SubscriptionTier.freelancer;
      case 'TEAM':
        return SubscriptionTier.team;
      case 'ENTERPRISE':
        return SubscriptionTier.enterprise;
      default:
        throw ArgumentError('Unknown subscription tier: $value');
    }
  }

  /// Get display name for the tier
  String get displayName {
    switch (this) {
      case SubscriptionTier.freelancer:
        return 'Freelancer';
      case SubscriptionTier.team:
        return 'Team';
      case SubscriptionTier.enterprise:
        return 'Enterprise';
    }
  }

  /// Get SMS quota for this tier
  int get smsQuota {
    switch (this) {
      case SubscriptionTier.freelancer:
        return 50;
      case SubscriptionTier.team:
        return 250;
      case SubscriptionTier.enterprise:
        return 1000;
    }
  }

  /// Check if this tier allows team member invitations
  bool get allowsTeamInvitations {
    switch (this) {
      case SubscriptionTier.freelancer:
        return false;
      case SubscriptionTier.team:
      case SubscriptionTier.enterprise:
        return true;
    }
  }

  /// Check if this tier allows salon changes
  bool get allowsSalonChanges {
    switch (this) {
      case SubscriptionTier.freelancer:
      case SubscriptionTier.team:
        return false;
      case SubscriptionTier.enterprise:
        return true;
    }
  }

  /// Get maximum team members (null means unlimited)
  int? get maxTeamMembers {
    switch (this) {
      case SubscriptionTier.freelancer:
        return 1;
      case SubscriptionTier.team:
      case SubscriptionTier.enterprise:
        return null; // unlimited
    }
  }

  /// Get description for this tier
  String get description {
    switch (this) {
      case SubscriptionTier.freelancer:
        return 'Perfect for individual groomers. Includes $smsQuota SMS reminders per month.';
      case SubscriptionTier.team:
        return 'Great for small teams. Includes $smsQuota SMS reminders per month and unlimited team members.';
      case SubscriptionTier.enterprise:
        return 'Full-featured plan for growing businesses. Includes $smsQuota SMS reminders per month, unlimited team members, and salon management features.';
    }
  }

  /// Get features list for this tier
  List<String> get features {
    switch (this) {
      case SubscriptionTier.freelancer:
        return [
          '$smsQuota SMS reminders per month',
          'Basic appointment management',
          'Client database',
          'Single user access',
        ];
      case SubscriptionTier.team:
        return [
          '$smsQuota SMS reminders per month',
          'Full appointment management',
          'Client database',
          'Unlimited team members',
          'Team collaboration tools',
          'Advanced reporting',
        ];
      case SubscriptionTier.enterprise:
        return [
          '$smsQuota SMS reminders per month',
          'Full appointment management',
          'Client database',
          'Unlimited team members',
          'Team collaboration tools',
          'Advanced reporting',
          'Multiple salon management',
          'Priority support',
          'Custom integrations',
        ];
    }
  }

  /// Check if team size is within limits for this tier
  bool isTeamSizeAllowed(int currentTeamSize) {
    return maxTeamMembers?.let((max) => currentTeamSize <= max) ?? true;
  }

  /// Get monthly price for this tier (in RON)
  double get monthlyPrice {
    switch (this) {
      case SubscriptionTier.freelancer:
        return 0.0; // Free tier
      case SubscriptionTier.team:
        return 29.99;
      case SubscriptionTier.enterprise:
        return 99.99;
    }
  }

  /// Get color associated with this tier
  String get colorHex {
    switch (this) {
      case SubscriptionTier.freelancer:
        return '#4CAF50'; // Green
      case SubscriptionTier.team:
        return '#2196F3'; // Blue
      case SubscriptionTier.enterprise:
        return '#9C27B0'; // Purple
    }
  }

  /// Check if this is a paid tier
  bool get isPaid {
    return monthlyPrice > 0;
  }
}

/// Extension to add null-safe let function
extension NullableExtension<T> on T? {
  R? let<R>(R Function(T) transform) {
    final value = this;
    return value != null ? transform(value) : null;
  }
}

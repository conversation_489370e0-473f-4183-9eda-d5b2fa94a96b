import 'subscription_tier.dart';

/// Enum representing subscription status
enum SubscriptionStatus {
  active('ACTIVE'),
  cancelled('CANCELLED'),
  suspended('SUSPENDED'),
  expired('EXPIRED'),
  trial('TRIAL');

  const SubscriptionStatus(this.value);
  final String value;

  static SubscriptionStatus fromString(String value) {
    switch (value.toUpperCase()) {
      case 'ACTIVE':
        return SubscriptionStatus.active;
      case 'CANCELLED':
        return SubscriptionStatus.cancelled;
      case 'SUSPENDED':
        return SubscriptionStatus.suspended;
      case 'EXPIRED':
        return SubscriptionStatus.expired;
      case 'TRIAL':
        return SubscriptionStatus.trial;
      default:
        throw ArgumentError('Unknown subscription status: $value');
    }
  }

  String get displayName {
    switch (this) {
      case SubscriptionStatus.active:
        return 'Active';
      case SubscriptionStatus.cancelled:
        return 'Cancelled';
      case SubscriptionStatus.suspended:
        return 'Suspended';
      case SubscriptionStatus.expired:
        return 'Expired';
      case SubscriptionStatus.trial:
        return 'Trial';
    }
  }
}

/// Enum representing billing cycles
enum BillingCycle {
  monthly('MONTHLY'),
  quarterly('QUARTERLY'),
  yearly('YEARLY');

  const BillingCycle(this.value);
  final String value;

  static BillingCycle fromString(String value) {
    switch (value.toUpperCase()) {
      case 'MONTHLY':
        return BillingCycle.monthly;
      case 'QUARTERLY':
        return BillingCycle.quarterly;
      case 'YEARLY':
        return BillingCycle.yearly;
      default:
        throw ArgumentError('Unknown billing cycle: $value');
    }
  }

  String get displayName {
    switch (this) {
      case BillingCycle.monthly:
        return 'Monthly';
      case BillingCycle.quarterly:
        return 'Quarterly';
      case BillingCycle.yearly:
        return 'Yearly';
    }
  }

  int get months {
    switch (this) {
      case BillingCycle.monthly:
        return 1;
      case BillingCycle.quarterly:
        return 3;
      case BillingCycle.yearly:
        return 12;
    }
  }
}

/// Model representing a salon's subscription
class SalonSubscription {
  final String id;
  final String salonId;
  final SubscriptionTier tier;
  final SubscriptionStatus status;
  final DateTime startDate;
  final DateTime? endDate;
  final bool isActive;
  final bool autoRenew;
  final double monthlyPrice;
  final BillingCycle billingCycle;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? lastBillingDate;
  final DateTime? nextBillingDate;
  final DateTime? trialEndDate;
  final DateTime? cancelledAt;
  final String? cancellationReason;

  SalonSubscription({
    required this.id,
    required this.salonId,
    required this.tier,
    required this.status,
    required this.startDate,
    this.endDate,
    required this.isActive,
    required this.autoRenew,
    required this.monthlyPrice,
    required this.billingCycle,
    required this.createdAt,
    required this.updatedAt,
    this.lastBillingDate,
    this.nextBillingDate,
    this.trialEndDate,
    this.cancelledAt,
    this.cancellationReason,
  });

  /// Create from JSON
  factory SalonSubscription.fromJson(Map<String, dynamic> json) {
    return SalonSubscription(
      id: json['id'] ?? '',
      salonId: json['salonId'] ?? '',
      tier: SubscriptionTier.fromString(json['tier'] ?? 'FREELANCER'),
      status: SubscriptionStatus.fromString(json['status'] ?? 'ACTIVE'),
      startDate: DateTime.parse(json['startDate']),
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate']) : null,
      isActive: json['isActive'] ?? true,
      autoRenew: json['autoRenew'] ?? true,
      monthlyPrice: (json['monthlyPrice'] ?? 0.0).toDouble(),
      billingCycle: BillingCycle.fromString(json['billingCycle'] ?? 'MONTHLY'),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      lastBillingDate: json['lastBillingDate'] != null ? DateTime.parse(json['lastBillingDate']) : null,
      nextBillingDate: json['nextBillingDate'] != null ? DateTime.parse(json['nextBillingDate']) : null,
      trialEndDate: json['trialEndDate'] != null ? DateTime.parse(json['trialEndDate']) : null,
      cancelledAt: json['cancelledAt'] != null ? DateTime.parse(json['cancelledAt']) : null,
      cancellationReason: json['cancellationReason'],
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'salonId': salonId,
      'tier': tier.value,
      'status': status.value,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'isActive': isActive,
      'autoRenew': autoRenew,
      'monthlyPrice': monthlyPrice,
      'billingCycle': billingCycle.value,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'lastBillingDate': lastBillingDate?.toIso8601String(),
      'nextBillingDate': nextBillingDate?.toIso8601String(),
      'trialEndDate': trialEndDate?.toIso8601String(),
      'cancelledAt': cancelledAt?.toIso8601String(),
      'cancellationReason': cancellationReason,
    };
  }

  /// Check if subscription is currently active and valid
  bool get isCurrentlyActive {
    final now = DateTime.now();
    return isActive && 
           status == SubscriptionStatus.active &&
           now.isAfter(startDate) &&
           (endDate == null || now.isBefore(endDate!));
  }

  /// Check if subscription is in trial period
  bool get isInTrial {
    final now = DateTime.now();
    return trialEndDate != null && now.isBefore(trialEndDate!);
  }

  /// Check if subscription has expired
  bool get isExpired {
    final now = DateTime.now();
    return endDate != null && now.isAfter(endDate!);
  }

  /// Get days remaining in trial (if applicable)
  int? get trialDaysRemaining {
    if (trialEndDate == null) return null;
    final now = DateTime.now();
    if (now.isAfter(trialEndDate!)) return 0;
    return trialEndDate!.difference(now).inDays;
  }

  /// Get subscription features
  SubscriptionFeatures get features {
    return SubscriptionFeatures.fromTier(tier);
  }

  /// Copy with method for immutable updates
  SalonSubscription copyWith({
    String? id,
    String? salonId,
    SubscriptionTier? tier,
    SubscriptionStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    bool? isActive,
    bool? autoRenew,
    double? monthlyPrice,
    BillingCycle? billingCycle,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastBillingDate,
    DateTime? nextBillingDate,
    DateTime? trialEndDate,
    DateTime? cancelledAt,
    String? cancellationReason,
  }) {
    return SalonSubscription(
      id: id ?? this.id,
      salonId: salonId ?? this.salonId,
      tier: tier ?? this.tier,
      status: status ?? this.status,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isActive: isActive ?? this.isActive,
      autoRenew: autoRenew ?? this.autoRenew,
      monthlyPrice: monthlyPrice ?? this.monthlyPrice,
      billingCycle: billingCycle ?? this.billingCycle,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastBillingDate: lastBillingDate ?? this.lastBillingDate,
      nextBillingDate: nextBillingDate ?? this.nextBillingDate,
      trialEndDate: trialEndDate ?? this.trialEndDate,
      cancelledAt: cancelledAt ?? this.cancelledAt,
      cancellationReason: cancellationReason ?? this.cancellationReason,
    );
  }
}

/// Model representing subscription features
class SubscriptionFeatures {
  final SubscriptionTier tier;
  final int smsQuota;
  final bool canInviteTeamMembers;
  final bool canChangeSalon;
  final int? maxTeamMembers;
  final String description;

  SubscriptionFeatures({
    required this.tier,
    required this.smsQuota,
    required this.canInviteTeamMembers,
    required this.canChangeSalon,
    this.maxTeamMembers,
    required this.description,
  });

  factory SubscriptionFeatures.fromTier(SubscriptionTier tier) {
    return SubscriptionFeatures(
      tier: tier,
      smsQuota: tier.smsQuota,
      canInviteTeamMembers: tier.allowsTeamInvitations,
      canChangeSalon: tier.allowsSalonChanges,
      maxTeamMembers: tier.maxTeamMembers,
      description: tier.description,
    );
  }

  factory SubscriptionFeatures.fromJson(Map<String, dynamic> json) {
    return SubscriptionFeatures(
      tier: SubscriptionTier.fromString(json['tier']),
      smsQuota: json['smsQuota'] ?? 0,
      canInviteTeamMembers: json['canInviteTeamMembers'] ?? false,
      canChangeSalon: json['canChangeSalon'] ?? false,
      maxTeamMembers: json['maxTeamMembers'],
      description: json['description'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'tier': tier.value,
      'smsQuota': smsQuota,
      'canInviteTeamMembers': canInviteTeamMembers,
      'canChangeSalon': canChangeSalon,
      'maxTeamMembers': maxTeamMembers,
      'description': description,
    };
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/subscription_provider.dart';
import '../../models/subscription_tier.dart';
import '../../widgets/subscription/subscription_card.dart';
import '../../widgets/subscription/subscription_features_card.dart';
import '../../config/theme/app_theme.dart';
import 'subscription_upgrade_screen.dart';

/// Screen for managing salon subscription
class SubscriptionManagementScreen extends StatefulWidget {
  const SubscriptionManagementScreen({super.key});

  @override
  State<SubscriptionManagementScreen> createState() => _SubscriptionManagementScreenState();
}

class _SubscriptionManagementScreenState extends State<SubscriptionManagementScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<SubscriptionProvider>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Subscription Management'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<SubscriptionProvider>().refresh();
            },
          ),
        ],
      ),
      body: Consumer<SubscriptionProvider>(
        builder: (context, subscriptionProvider, child) {
          if (subscriptionProvider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (subscriptionProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red[300],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading subscription',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    subscriptionProvider.error!,
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      subscriptionProvider.refresh();
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: () => subscriptionProvider.refresh(),
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Current Subscription Card
                  if (subscriptionProvider.currentSubscription != null)
                    SubscriptionCard(
                      subscription: subscriptionProvider.currentSubscription!,
                      onUpgrade: _showUpgradeOptions,
                      onCancel: _showCancelDialog,
                    )
                  else
                    _buildNoSubscriptionCard(),

                  const SizedBox(height: 16),

                  // Features Card
                  if (subscriptionProvider.features != null)
                    SubscriptionFeaturesCard(
                      features: subscriptionProvider.features!,
                    ),

                  const SizedBox(height: 16),

                  // Trial Information
                  if (subscriptionProvider.isInTrial)
                    _buildTrialCard(subscriptionProvider),

                  const SizedBox(height: 16),

                  // Available Plans
                  _buildAvailablePlans(),

                  const SizedBox(height: 16),

                  // Subscription History
                  _buildSubscriptionHistory(subscriptionProvider),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildNoSubscriptionCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(
              Icons.info_outline,
              size: 48,
              color: Colors.orange[300],
            ),
            const SizedBox(height: 16),
            Text(
              'No Active Subscription',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'You currently don\'t have an active subscription. Choose a plan to get started.',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _showUpgradeOptions,
              child: const Text('Choose Plan'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrialCard(SubscriptionProvider provider) {
    final daysRemaining = provider.trialDaysRemaining ?? 0;
    
    return Card(
      color: Colors.blue[50],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              Icons.schedule,
              color: Colors.blue[700],
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Trial Period',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Colors.blue[700],
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    daysRemaining > 0
                        ? '$daysRemaining days remaining'
                        : 'Trial expired',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
            if (daysRemaining > 0)
              TextButton(
                onPressed: _showUpgradeOptions,
                child: const Text('Upgrade Now'),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAvailablePlans() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Available Plans',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        const SizedBox(height: 12),
        ...SubscriptionTier.values.map((tier) => _buildPlanCard(tier)),
      ],
    );
  }

  Widget _buildPlanCard(SubscriptionTier tier) {
    final isCurrentTier = context.read<SubscriptionProvider>().currentTier == tier;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Color(int.parse(tier.colorHex.substring(1), radix: 16) + 0xFF000000),
          child: Text(
            tier.displayName[0],
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        title: Text(tier.displayName),
        subtitle: Text(tier.description),
        trailing: isCurrentTier
            ? const Chip(
                label: Text('Current'),
                backgroundColor: Colors.green,
              )
            : Text(
                tier.isPaid ? '${tier.monthlyPrice.toStringAsFixed(2)} RON/month' : 'Free',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
        onTap: isCurrentTier ? null : () => _upgradeTo(tier),
      ),
    );
  }

  Widget _buildSubscriptionHistory(SubscriptionProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Subscription History',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            TextButton(
              onPressed: () {
                provider.loadSubscriptionHistory();
              },
              child: const Text('Load History'),
            ),
          ],
        ),
        const SizedBox(height: 12),
        if (provider.history.isEmpty)
          const Text('No subscription history available.')
        else
          ...provider.history.map((subscription) => Card(
            margin: const EdgeInsets.only(bottom: 8),
            child: ListTile(
              title: Text(subscription.tier.displayName),
              subtitle: Text(
                '${subscription.startDate.toString().split(' ')[0]} - '
                '${subscription.endDate?.toString().split(' ')[0] ?? 'Ongoing'}',
              ),
              trailing: Chip(
                label: Text(subscription.status.displayName),
                backgroundColor: subscription.status == SubscriptionStatus.active
                    ? Colors.green[100]
                    : Colors.grey[200],
              ),
            ),
          )),
      ],
    );
  }

  void _showUpgradeOptions() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const SubscriptionUpgradeScreen(),
      ),
    );
  }

  void _upgradeTo(SubscriptionTier tier) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SubscriptionUpgradeScreen(selectedTier: tier),
      ),
    );
  }

  void _showCancelDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Subscription'),
        content: const Text(
          'Are you sure you want to cancel your subscription? '
          'You will lose access to premium features.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Keep Subscription'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _cancelSubscription();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Cancel Subscription'),
          ),
        ],
      ),
    );
  }

  void _cancelSubscription() async {
    final provider = context.read<SubscriptionProvider>();
    final success = await provider.cancelSubscription(
      reason: 'User requested cancellation',
    );

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Subscription cancelled successfully'),
          backgroundColor: Colors.green,
        ),
      );
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(provider.error ?? 'Failed to cancel subscription'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}

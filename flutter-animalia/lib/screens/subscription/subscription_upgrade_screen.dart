import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/subscription_provider.dart';
import '../../models/subscription_tier.dart';
import '../../config/theme/app_theme.dart';

/// Screen for upgrading subscription
class SubscriptionUpgradeScreen extends StatefulWidget {
  final SubscriptionTier? selectedTier;

  const SubscriptionUpgradeScreen({
    super.key,
    this.selectedTier,
  });

  @override
  State<SubscriptionUpgradeScreen> createState() => _SubscriptionUpgradeScreenState();
}

class _SubscriptionUpgradeScreenState extends State<SubscriptionUpgradeScreen> {
  SubscriptionTier? _selectedTier;
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _selectedTier = widget.selectedTier;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Upgrade Subscription'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Consumer<SubscriptionProvider>(
        builder: (context, subscriptionProvider, child) {
          final currentTier = subscriptionProvider.currentTier;

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Current plan info
                if (subscriptionProvider.currentSubscription != null)
                  Card(
                    color: Colors.blue[50],
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Current Plan',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            currentTier.displayName,
                            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                              color: Color(int.parse(currentTier.colorHex.substring(1), radix: 16) + 0xFF000000),
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '${currentTier.smsQuota} SMS per month',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                        ],
                      ),
                    ),
                  ),

                const SizedBox(height: 24),

                // Available plans
                Text(
                  'Choose Your New Plan',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),

                const SizedBox(height: 16),

                ...SubscriptionTier.values
                    .where((tier) => tier.ordinal > currentTier.ordinal)
                    .map((tier) => _buildPlanCard(tier)),

                const SizedBox(height: 24),

                // Upgrade button
                if (_selectedTier != null)
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _isProcessing ? null : _upgradeSubscription,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Color(int.parse(_selectedTier!.colorHex.substring(1), radix: 16) + 0xFF000000),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: _isProcessing
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Text(
                              'Upgrade to ${_selectedTier!.displayName} - ${_selectedTier!.monthlyPrice.toStringAsFixed(2)} RON/month',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                    ),
                  ),

                const SizedBox(height: 16),

                // Disclaimer
                Card(
                  color: Colors.orange[50],
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.info_outline, color: Colors.orange[700]),
                            const SizedBox(width: 8),
                            Text(
                              'Important Information',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.orange[700],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '• Your upgrade will take effect immediately\n'
                          '• You will be charged the new rate on your next billing cycle\n'
                          '• Your SMS quota will be updated right away\n'
                          '• You can downgrade at any time',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildPlanCard(SubscriptionTier tier) {
    final isSelected = _selectedTier == tier;
    final tierColor = Color(int.parse(tier.colorHex.substring(1), radix: 16) + 0xFF000000);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: isSelected ? 8 : 2,
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedTier = tier;
          });
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: isSelected
                ? Border.all(color: tierColor, width: 2)
                : null,
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CircleAvatar(
                      backgroundColor: tierColor,
                      child: Text(
                        tier.displayName[0],
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            tier.displayName,
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: tierColor,
                            ),
                          ),
                          Text(
                            tier.isPaid 
                                ? '${tier.monthlyPrice.toStringAsFixed(2)} RON/month'
                                : 'Free',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (isSelected)
                      Icon(
                        Icons.check_circle,
                        color: tierColor,
                        size: 32,
                      ),
                  ],
                ),

                const SizedBox(height: 12),

                Text(
                  tier.description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),

                const SizedBox(height: 12),

                // Features
                ...tier.features.take(3).map((feature) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check,
                        color: tierColor,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          feature,
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ),
                    ],
                  ),
                )),

                if (tier.features.length > 3)
                  Text(
                    '+ ${tier.features.length - 3} more features',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: tierColor,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _upgradeSubscription() async {
    if (_selectedTier == null) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      final subscriptionProvider = context.read<SubscriptionProvider>();
      final success = await subscriptionProvider.upgradeSubscription(
        newTier: _selectedTier!,
        newPrice: _selectedTier!.monthlyPrice,
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Successfully upgraded to ${_selectedTier!.displayName}!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop();
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(subscriptionProvider.error ?? 'Failed to upgrade subscription'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }
}
